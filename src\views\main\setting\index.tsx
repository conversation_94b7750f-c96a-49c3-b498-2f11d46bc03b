import {
  Layout,
  Menu,
  Avatar,
  Typography,
  Form,
  Input,
  Button,
  Row,
  Col,
  Breadcrumb,
} from "antd";
import {
  UserOutlined,
  MailOutlined,
  LockOutlined,
  SettingOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import { Outlet, useNavigate } from "react-router-dom";

const { Sider, Content } = Layout;
const { Title, Text } = Typography;

export const SettingView = () => {
  const navigate = useNavigate();

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <Layout style={{ height: "calc(100vh - 100px)", backgroundColor: "#fff" }}>
      {/* SIDEBAR */}
      <Sider
        width={300}
        style={{
          background: "#fff",
          padding: "24px 16px",
        }}
      >
        <div style={{ textAlign: "center", marginBottom: 24 }}>
          <Avatar
            size={80}
            style={{ backgroundColor: "#FCAE16", fontSize: 32 }}
          >
            V
          </Avatar>
          <Title level={4} style={{ margin: "16px 0 0" }}>
            V<PERSON>c
          </Title>
          <Text type="secondary"><EMAIL></Text>
        </div>

        <Menu
          mode="vertical"
          defaultSelectedKeys={["1"]}
          className="layout-page-sider-menu"
        >
          <Menu.Item
            key="1"
            icon={<UserOutlined />}
            onClick={() => handleNavigate("/settings/info")}
          >
            Thông tin cá nhân
          </Menu.Item>
          <Menu.Item key="2" icon={<FileTextOutlined />}>
            Đơn hàng của tôi
          </Menu.Item>
          <Menu.Item key="3" icon={<FileTextOutlined />}>
            Danh sách giao dịch
          </Menu.Item>
          <Menu.Item
            key="4"
            icon={<SettingOutlined />}
            onClick={() => handleNavigate("/settings/security")}
          >
            Cấu hình bảo mật
          </Menu.Item>
          <Menu.Item key="5" icon={<LockOutlined />}>
            Điều khoản bảo mật
          </Menu.Item>
          <Menu.Item key="6" icon={<SettingOutlined />}>
            Gói bảo mật
          </Menu.Item>
        </Menu>
      </Sider>

      {/* MAIN CONTENT */}
      <Layout
        style={{
          background: "#fff",
          height: "calc(100vh - 100px)",
          overflow: "auto",
        }}
      >
        <Content style={{ padding: "24px" }}>
          <Breadcrumb
            separator=">"
            items={[
              {
                title: (
                  <>
                    <SettingOutlined />
                    <span>Cài đặt</span>
                  </>
                ),
              },
              {
                title: "Cấu hình bảo mật",
              },
            ]}
          />
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};
