import { HomeOutlined, UserOutlined } from "@ant-design/icons";
import { IRouter } from "~/routers";
import {
  ContactView,
  FaqView,
  ProfileView,
  PrivacyView,
  TransactionListView,
  ProductSalesView,
  PaymentView,
  HomeView,
} from "~/views/main";
import { SettingView } from "~/views/main/setting";
import { SettingSecurityComponent } from "~/views/main/setting/sub-screen/SettingSecurityComponent";

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children,
});

export const mainRouter: IRouter[] = [
  createRoute("/", <ProductSalesView />, "Sản phẩm", <UserOutlined />),
  createRoute(
    "/transaction-list",
    <TransactionListView />,
    "<PERSON>h sách giao dịch",
    <UserOutlined />
  ),
  createRoute(
    "/privacy",
    <PrivacyView />,
    "Thông tin bảo mật",
    <UserOutlined />
  ),
  createRoute("/contact", <ContactView />, "Liên hệ tư vấn", <UserOutlined />),
  createRoute("/faq", <FaqView />, "Câu hỏi thường gặp", <UserOutlined />),
  createRoute(
    "/profile",
    <ProfileView />,
    "Hồ sơ cá nhân",
    <UserOutlined />,
    false
  ),

  createRoute(
    "/payment",
    <PaymentView />,
    "Thanh toán",
    <UserOutlined />,
    false
  ),

  createRoute(
    "/settings",
    <SettingView />,
    "Cài đặt",
    <UserOutlined />,
    false,
    [
      createRoute(
        "/settings/info",
        <ProfileView />,
        "Thông tin cá nhân",
        <UserOutlined />,
        false
      ),
      createRoute(
        "/settings/order",
        <TransactionListView />,
        "Đơn hàng của tôi",
        <UserOutlined />,
        false
      ),
      createRoute(
        "/settings/security",
        <SettingSecurityComponent />,
        "Cấu hình bảo mật",
        <UserOutlined />,
        false
      ),
      createRoute(
        "/settings/privacy",
        <PrivacyView />,
        "Điều khoản bảo mật",
        <UserOutlined />,
        false
      ),
      createRoute(
        "/settings/payment",
        <PaymentView />,
        "Gói bảo mật",
        <UserOutlined />,
        false
      ),
    ]
  )
];
