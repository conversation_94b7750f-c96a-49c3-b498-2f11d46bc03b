

const getKeyEnumByValue = <T = any>(targetEnum: T, valueFind: any) => {
    return Object.keys(targetEnum)[Object.values(targetEnum).indexOf(valueFind)] || "";
}

const getColorPercent = (percent: number, reverse = false) => {
    if (reverse) {
        if (percent <= 50) return 'green'
        if (percent <= 100) return 'orange'
        return 'red'
    }
    if (percent >= 0 && percent <= 50) return 'red'
    if (percent >= 100) return 'green'
    if (percent > 50) return 'orange'
    return 'red'
}

const getColorStatus = (status: string) => {
    const s = status.toUpperCase()
    switch (s) {
        case 'DONE':
            return 'green'
        case 'DOING':
            return 'orange'
        case 'PENDING':
            return 'blue'
        case 'FAILED':
            return 'red'
        case 'SENT':
            return 'green'
        case 'SIGNED':
            return 'orange'
        case 'DRAFT':
            return 'blue'
        case 'ACTIVE':
            return 'green'
        case 'APPROVED':
            return 'green'
        case 'INACTIVE':
            return 'orange'
        default:
            return 'default'
    }
}


export {
    getKeyEnumByValue,
    getColorPercent,
    getColorStatus,
}