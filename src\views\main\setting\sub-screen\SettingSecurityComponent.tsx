import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Collapse,
  DatePicker,
  Form,
  Input,
  Row,
  Select,
  Space,
  Tag,
  Typography,
} from "antd";
import TextArea from "antd/es/input/TextArea";
import BaseView from "~/components/BaseView";
import exampleKey from "../example.key.json";
import BaseTable from "~/components/BaseTable";
import {
  DeleteOutlined,
  EditOutlined,
  ExportOutlined,
  FilterOutlined,
  PlusOutlined,
  StopOutlined,
} from "@ant-design/icons";
import { title } from "process";
import { render } from "react-dom";
import { getColorStatus } from "~/common/utils/common.utils";
import { CreateSettingSecurityModal } from "../components/CreateSettingSecurityModal";
import { useState } from "react";

const { Text, Title } = Typography;

export const SettingSecurityComponent = () => {
  const [openCreateModal, setOpenCreateModal] = useState(false);

  const handleOpenCreateModal = () => {
    setOpenCreateModal(true);
  };

  const columns = [
    {
      title: "STT",
      dataIndex: "stt",
      key: "stt",
      render: (_, record, index) => index + 1,
    },
    {
      title: "Tên cấu hình",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Mô tả",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Trạng thái",
      dataIndex: "status",
      key: "status",
      render: (status) => {
        return <Tag color={getColorStatus(status)}>{status}</Tag>;
      },
    },
    {
      title: "Ngày tạo",
      dataIndex: "createdAt",
      key: "createdAt",
    },
  ];

  const columnsAction = [
    {
      title: "Hành động",
      dataIndex: "action",
      key: "action",
      render: () => {
        return (
          <Space>
            <Button type="primary" icon={<EditOutlined />}></Button>
            <Button danger type="default" icon={<DeleteOutlined />}></Button>
            <Button danger icon={<StopOutlined />}></Button>
          </Space>
        );
      },
    },
  ];

  const data = [
    {
      key: "1",
      name: "Cấu hình JWT",
      description: "Cấu hình token xác thực người dùng",
      status: "Active",
      createdAt: "2024-06-01 09:00",
    },
    {
      key: "2",
      name: "Giới hạn IP",
      description: "Chỉ cho phép IP nội bộ truy cập",
      status: "Inactive",
      createdAt: "2024-06-02 10:30",
    },
    {
      key: "3",
      name: "Xác thực 2 bước",
      description: "Bắt buộc xác thực 2FA khi đăng nhập",
      status: "Active",
      createdAt: "2024-06-03 11:45",
    },
    {
      key: "4",
      name: "Mã hóa dữ liệu",
      description: "Dữ liệu lưu trữ được mã hóa AES-256",
      status: "Active",
      createdAt: "2024-06-04 14:15",
    },
    {
      key: "5",
      name: "Chống brute-force",
      description: "Giới hạn số lần đăng nhập sai",
      status: "Active",
      createdAt: "2024-06-05 08:20",
    },
    {
      key: "6",
      name: "Giới hạn API",
      description: "Giới hạn số lượng request mỗi phút",
      status: "Inactive",
      createdAt: "2024-06-06 16:00",
    },
    {
      key: "7",
      name: "Cảnh báo đăng nhập lạ",
      description: "Gửi cảnh báo khi phát hiện thiết bị mới",
      status: "Active",
      createdAt: "2024-06-07 17:40",
    },
    {
      key: "8",
      name: "Mật khẩu mạnh",
      description: "Yêu cầu mật khẩu có ký tự đặc biệt",
      status: "Active",
      createdAt: "2024-06-08 09:10",
    },
    {
      key: "9",
      name: "Tự động đăng xuất",
      description: "Phiên sẽ hết hạn sau 30 phút không Active",
      status: "Active",
      createdAt: "2024-06-09 13:30",
    },
    {
      key: "10",
      name: "Giới hạn thiết bị",
      description: "Chỉ cho phép đăng nhập từ 3 thiết bị",
      status: "Inactive",
      createdAt: "2024-06-10 15:00",
    },
  ];

  return (
    <BaseView>
      <CreateSettingSecurityModal open={openCreateModal} onClose={() => setOpenCreateModal(false)} />

      <Collapse size="small">
        <Collapse.Panel header="Thông tin khóa bảo mật" key="1">
          <Row gutter={16}>
            <Col span={24}>
              <Title level={5} style={{ marginTop: 4 }}>
                Public key:
              </Title>
              <TextArea
                rows={8}
                placeholder="Public key"
                disabled
                value={exampleKey.publicKey}
              />
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 4 }}>
            <Col span={24}>
              <Title level={5}>API Key:</Title>
              <TextArea
                rows={2}
                placeholder="API Key"
                disabled
                value={exampleKey.apiKey}
              />
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Collapse size="small" style={{ marginTop: 16 }}>
        <Collapse.Panel header="Bộ lọc" key="1">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Tên cấu hình">
                <Input placeholder="Tên cấu hình" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Trạng thái">
                <Select
                  style={{ width: "100%" }}
                  placeholder="Trạng thái"
                  options={[
                    { value: "1", label: "Active" },
                    { value: "2", label: "Inactive" },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Ngày tạo">
                {/* Ngày tạo từ ngày đến ngày */}
                <DatePicker.RangePicker
                  style={{ width: "100%" }}
                  placeholder={["Từ ngày", "Đến ngày"]}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 4 }}>
            <Col span={24} style={{ textAlign: "center" }}>
              <Button type="primary" icon={<FilterOutlined />}>
                Lọc
              </Button>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>

      <Space style={{ marginTop: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => setOpenCreateModal(true)}>
          Tạo mới
        </Button>
        {/* Xuất excel */}
        <Button icon={<ExportOutlined />}>Xuất Excel</Button>
      </Space>
      {/* Danh sách cấu hình */}
      <BaseTable
        size="small"
        style={{ marginTop: 16 }}
        columns={[...columns, ...columnsAction]}
        data={data}
        isLoading={false}
        total={data.length}
        defaultPageSize={10}
      ></BaseTable>
    </BaseView>
  );
};
